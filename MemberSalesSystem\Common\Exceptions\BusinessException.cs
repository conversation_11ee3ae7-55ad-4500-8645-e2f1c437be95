namespace MemberSalesSystem.Common.Exceptions;

/// <summary>
/// 业务异常类
/// </summary>
public class BusinessException : Exception
{
    public string ErrorCode { get; }
    
    public BusinessException(string message) : base(message)
    {
        ErrorCode = "BUSINESS_ERROR";
    }
    
    public BusinessException(string errorCode, string message) : base(message)
    {
        ErrorCode = errorCode;
    }
    
    public BusinessException(string message, Exception innerException) : base(message, innerException)
    {
        ErrorCode = "BUSINESS_ERROR";
    }
    
    public BusinessException(string errorCode, string message, Exception innerException) : base(message, innerException)
    {
        ErrorCode = errorCode;
    }
}

/// <summary>
/// 验证异常类
/// </summary>
public class ValidationException : BusinessException
{
    public ValidationException(string message) : base("VALIDATION_ERROR", message)
    {
    }
}

/// <summary>
/// 权限异常类
/// </summary>
public class UnauthorizedException : BusinessException
{
    public UnauthorizedException(string message) : base("UNAUTHORIZED", message)
    {
    }
}

/// <summary>
/// 资源不存在异常类
/// </summary>
public class NotFoundException : BusinessException
{
    public NotFoundException(string message) : base("NOT_FOUND", message)
    {
    }
}

/// <summary>
/// 库存不足异常类
/// </summary>
public class InsufficientStockException : BusinessException
{
    public InsufficientStockException(string message) : base("INSUFFICIENT_STOCK", message)
    {
    }
}

/// <summary>
/// 余额不足异常类
/// </summary>
public class InsufficientBalanceException : BusinessException
{
    public InsufficientBalanceException(string message) : base("INSUFFICIENT_BALANCE", message)
    {
    }
}
