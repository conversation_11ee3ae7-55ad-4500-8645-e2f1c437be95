using MemberSalesSystem.Common.Enums;
using MemberSalesSystem.Common.Models;
using MemberSalesSystem.Data.Entities;

namespace MemberSalesSystem.Business.Services;

/// <summary>
/// 用户服务接口
/// </summary>
public interface IUserService
{
    /// <summary>
    /// 用户登录
    /// </summary>
    Task<ServiceResult<User>> LoginAsync(string username, string password);
    
    /// <summary>
    /// 创建用户
    /// </summary>
    Task<ServiceResult<User>> CreateUserAsync(string username, string password, string? realName, 
        string? phone, string? email, UserRole role, int createdById);
    
    /// <summary>
    /// 更新用户信息
    /// </summary>
    Task<ServiceResult<User>> UpdateUserAsync(int userId, string? realName, string? phone, string? email);
    
    /// <summary>
    /// 修改密码
    /// </summary>
    Task<ServiceResult> ChangePasswordAsync(int userId, string oldPassword, string newPassword);
    
    /// <summary>
    /// 重置密码（管理员操作）
    /// </summary>
    Task<ServiceResult> ResetPasswordAsync(int userId, string newPassword, int operatorId);
    
    /// <summary>
    /// 获取用户信息
    /// </summary>
    Task<ServiceResult<User>> GetUserAsync(int userId);
    
    /// <summary>
    /// 根据角色获取用户列表
    /// </summary>
    Task<ServiceResult<List<User>>> GetUsersByRoleAsync(UserRole role);
    
    /// <summary>
    /// 获取用户创建的子用户列表
    /// </summary>
    Task<ServiceResult<List<User>>> GetCreatedUsersAsync(int creatorId);
    
    /// <summary>
    /// 启用/禁用用户
    /// </summary>
    Task<ServiceResult> SetUserActiveStatusAsync(int userId, bool isActive, int operatorId);
    
    /// <summary>
    /// 删除用户
    /// </summary>
    Task<ServiceResult> DeleteUserAsync(int userId, int operatorId);
    
    /// <summary>
    /// 检查用户权限
    /// </summary>
    Task<ServiceResult<bool>> CheckPermissionAsync(int userId, UserRole requiredRole);
    
    /// <summary>
    /// 获取用户余额
    /// </summary>
    Task<ServiceResult<decimal>> GetUserBalanceAsync(int userId);
}
