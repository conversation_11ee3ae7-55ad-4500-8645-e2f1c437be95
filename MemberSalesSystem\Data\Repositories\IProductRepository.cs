using MemberSalesSystem.Data.Entities;

namespace MemberSalesSystem.Data.Repositories;

/// <summary>
/// 商品仓储接口
/// </summary>
public interface IProductRepository : IRepository<Product>
{
    /// <summary>
    /// 根据分类获取商品列表
    /// </summary>
    Task<List<Product>> GetByCategoryAsync(string category);
    
    /// <summary>
    /// 搜索商品
    /// </summary>
    Task<List<Product>> SearchAsync(string keyword);
    
    /// <summary>
    /// 更新商品库存
    /// </summary>
    Task<bool> UpdateStockAsync(int productId, int newStock);
    
    /// <summary>
    /// 减少商品库存
    /// </summary>
    Task<bool> ReduceStockAsync(int productId, int quantity);
    
    /// <summary>
    /// 获取库存不足的商品列表
    /// </summary>
    Task<List<Product>> GetLowStockProductsAsync(int threshold = 10);
    
    /// <summary>
    /// 获取热销商品列表
    /// </summary>
    Task<List<Product>> GetPopularProductsAsync(int count = 10);
}
