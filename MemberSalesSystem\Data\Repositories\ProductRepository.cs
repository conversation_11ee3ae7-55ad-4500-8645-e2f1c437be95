using FreeSql;
using MemberSalesSystem.Data.Entities;
using Serilog;

namespace MemberSalesSystem.Data.Repositories;

/// <summary>
/// 商品仓储实现
/// </summary>
public class ProductRepository : Repository<Product>, IProductRepository
{
    public ProductRepository(IFreeSql freeSql, ILogger logger) : base(freeSql, logger)
    {
    }

    public async Task<List<Product>> GetByCategoryAsync(string category)
    {
        try
        {
            return await _freeSql.Select<Product>()
                .Where(p => p.Category == category && p.IsActive)
                .OrderBy(p => p.SortOrder)
                .ThenBy(p => p.Name)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "根据分类获取商品列表失败，分类: {Category}", category);
            throw;
        }
    }

    public async Task<List<Product>> SearchAsync(string keyword)
    {
        try
        {
            return await _freeSql.Select<Product>()
                .Where(p => p.IsActive && 
                    (p.Name.Contains(keyword) || 
                     (p.Description != null && p.Description.Contains(keyword)) ||
                     (p.Category != null && p.Category.Contains(keyword))))
                .OrderBy(p => p.SortOrder)
                .ThenBy(p => p.Name)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "搜索商品失败，关键词: {Keyword}", keyword);
            throw;
        }
    }

    public async Task<bool> UpdateStockAsync(int productId, int newStock)
    {
        try
        {
            var affectedRows = await _freeSql.Update<Product>()
                .Set(p => p.Stock, newStock)
                .Set(p => p.UpdatedAt, DateTime.Now)
                .Where(p => p.Id == productId)
                .ExecuteAffrowsAsync();

            var success = affectedRows > 0;
            _logger.Information("更新商品库存{Result}，商品ID: {ProductId}, 新库存: {Stock}", 
                success ? "成功" : "失败", productId, newStock);
            return success;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "更新商品库存失败，商品ID: {ProductId}, 库存: {Stock}", productId, newStock);
            throw;
        }
    }

    public async Task<bool> ReduceStockAsync(int productId, int quantity)
    {
        try
        {
            var affectedRows = await _freeSql.Update<Product>()
                .Set(p => p.Stock, p => p.Stock - quantity)
                .Set(p => p.UpdatedAt, DateTime.Now)
                .Where(p => p.Id == productId && p.Stock >= quantity)
                .ExecuteAffrowsAsync();

            var success = affectedRows > 0;
            _logger.Information("减少商品库存{Result}，商品ID: {ProductId}, 减少数量: {Quantity}", 
                success ? "成功" : "失败", productId, quantity);
            return success;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "减少商品库存失败，商品ID: {ProductId}, 数量: {Quantity}", productId, quantity);
            throw;
        }
    }

    public async Task<List<Product>> GetLowStockProductsAsync(int threshold = 10)
    {
        try
        {
            return await _freeSql.Select<Product>()
                .Where(p => p.Stock <= threshold && p.IsActive)
                .OrderBy(p => p.Stock)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "获取库存不足商品列表失败，阈值: {Threshold}", threshold);
            throw;
        }
    }

    public async Task<List<Product>> GetPopularProductsAsync(int count = 10)
    {
        try
        {
            // 根据订单明细统计热销商品
            return await _freeSql.Select<Product>()
                .LeftJoin<OrderItem>((p, oi) => p.Id == oi.ProductId)
                .Where(p => p.IsActive)
                .GroupBy((p, oi) => new { p.Id, p.Name, p.Price, p.Stock, p.Category })
                .OrderByDescending((p, oi) => FreeSql.SqlExt.Sum(oi.Quantity))
                .Take(count)
                .ToListAsync((p, oi) => p);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "获取热销商品列表失败，数量: {Count}", count);
            throw;
        }
    }
}
