using FreeSql;
using MemberSalesSystem.Common.Enums;
using MemberSalesSystem.Data.Entities;
using Serilog;

namespace MemberSalesSystem.Data.Repositories;

/// <summary>
/// 用户仓储实现
/// </summary>
public class UserRepository : Repository<User>, IUserRepository
{
    public UserRepository(IFreeSql freeSql, ILogger logger) : base(freeSql, logger)
    {
    }

    public async Task<User?> GetByUsernameAsync(string username)
    {
        try
        {
            return await _freeSql.Select<User>()
                .Where(u => u.Username == username && u.IsActive)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "根据用户名获取用户失败，用户名: {Username}", username);
            throw;
        }
    }

    public async Task<User?> ValidateUserAsync(string username, string password)
    {
        try
        {
            var user = await GetByUsernameAsync(username);
            if (user != null && BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
            {
                _logger.Information("用户登录验证成功，用户名: {Username}", username);
                return user;
            }
            
            _logger.Warning("用户登录验证失败，用户名: {Username}", username);
            return null;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "用户登录验证异常，用户名: {Username}", username);
            throw;
        }
    }

    public async Task<List<User>> GetByRoleAsync(UserRole role)
    {
        try
        {
            return await _freeSql.Select<User>()
                .Where(u => u.Role == role && u.IsActive)
                .OrderBy(u => u.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "根据角色获取用户列表失败，角色: {Role}", role);
            throw;
        }
    }

    public async Task<bool> UpdateBalanceAsync(int userId, decimal amount)
    {
        try
        {
            var affectedRows = await _freeSql.Update<User>()
                .Set(u => u.Balance, amount)
                .Set(u => u.UpdatedAt, DateTime.Now)
                .Where(u => u.Id == userId)
                .ExecuteAffrowsAsync();

            var success = affectedRows > 0;
            _logger.Information("更新用户余额{Result}，用户ID: {UserId}, 新余额: {Amount}", 
                success ? "成功" : "失败", userId, amount);
            return success;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "更新用户余额失败，用户ID: {UserId}, 金额: {Amount}", userId, amount);
            throw;
        }
    }

    public async Task<bool> IsUsernameExistsAsync(string username, int? excludeUserId = null)
    {
        try
        {
            var query = _freeSql.Select<User>()
                .Where(u => u.Username == username && u.IsActive);

            if (excludeUserId.HasValue)
            {
                query = query.Where(u => u.Id != excludeUserId.Value);
            }

            return await query.AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "检查用户名是否存在失败，用户名: {Username}", username);
            throw;
        }
    }

    public async Task<List<User>> GetCreatedUsersAsync(int creatorId)
    {
        try
        {
            return await _freeSql.Select<User>()
                .Where(u => u.CreatedById == creatorId && u.IsActive)
                .OrderBy(u => u.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "获取用户创建的子用户列表失败，创建者ID: {CreatorId}", creatorId);
            throw;
        }
    }
}
