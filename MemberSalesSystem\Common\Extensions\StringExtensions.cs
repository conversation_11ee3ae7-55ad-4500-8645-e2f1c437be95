namespace MemberSalesSystem.Common.Extensions;

/// <summary>
/// 字符串扩展方法
/// </summary>
public static class StringExtensions
{
    /// <summary>
    /// 生成订单号
    /// </summary>
    public static string GenerateOrderNumber()
    {
        return $"ORD{DateTime.Now:yyyyMMddHHmmss}{Random.Shared.Next(1000, 9999)}";
    }
    
    /// <summary>
    /// 生成充值单号
    /// </summary>
    public static string GenerateRechargeNumber()
    {
        return $"RCH{DateTime.Now:yyyyMMddHHmmss}{Random.Shared.Next(1000, 9999)}";
    }
    
    /// <summary>
    /// 检查字符串是否为空或null
    /// </summary>
    public static bool IsNullOrEmpty(this string? value)
    {
        return string.IsNullOrEmpty(value);
    }
    
    /// <summary>
    /// 检查字符串是否为空白或null
    /// </summary>
    public static bool IsNullOrWhiteSpace(this string? value)
    {
        return string.IsNullOrWhiteSpace(value);
    }
    
    /// <summary>
    /// 安全截取字符串
    /// </summary>
    public static string SafeSubstring(this string value, int maxLength)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;
            
        return value.Length <= maxLength ? value : value[..maxLength];
    }
}
