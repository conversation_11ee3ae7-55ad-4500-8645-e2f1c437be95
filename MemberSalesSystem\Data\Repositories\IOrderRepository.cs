using MemberSalesSystem.Common.Enums;
using MemberSalesSystem.Data.Entities;

namespace MemberSalesSystem.Data.Repositories;

/// <summary>
/// 订单仓储接口
/// </summary>
public interface IOrderRepository : IRepository<Order>
{
    /// <summary>
    /// 根据用户ID获取订单列表
    /// </summary>
    Task<List<Order>> GetByUserIdAsync(int userId);
    
    /// <summary>
    /// 根据订单号获取订单
    /// </summary>
    Task<Order?> GetByOrderNumberAsync(string orderNumber);
    
    /// <summary>
    /// 根据状态获取订单列表
    /// </summary>
    Task<List<Order>> GetByStatusAsync(OrderStatus status);
    
    /// <summary>
    /// 获取用户的订单统计信息
    /// </summary>
    Task<(int TotalOrders, decimal TotalAmount)> GetUserOrderStatsAsync(int userId);
    
    /// <summary>
    /// 获取指定时间范围内的订单列表
    /// </summary>
    Task<List<Order>> GetOrdersByDateRangeAsync(DateTime startDate, DateTime endDate);
    
    /// <summary>
    /// 更新订单状态
    /// </summary>
    Task<bool> UpdateStatusAsync(int orderId, OrderStatus status);
    
    /// <summary>
    /// 获取订单详情（包含订单明细）
    /// </summary>
    Task<Order?> GetOrderWithItemsAsync(int orderId);
}
