using FreeSql;
using MemberSalesSystem.Common.Enums;
using MemberSalesSystem.Data.Entities;
using Serilog;

namespace MemberSalesSystem.Data.Repositories;

/// <summary>
/// 订单仓储实现
/// </summary>
public class OrderRepository : Repository<Order>, IOrderRepository
{
    public OrderRepository(IFreeSql freeSql, ILogger logger) : base(freeSql, logger)
    {
    }

    public async Task<List<Order>> GetByUserIdAsync(int userId)
    {
        try
        {
            return await _freeSql.Select<Order>()
                .Where(o => o.UserId == userId && o.IsActive)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "根据用户ID获取订单列表失败，用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<Order?> GetByOrderNumberAsync(string orderNumber)
    {
        try
        {
            return await _freeSql.Select<Order>()
                .Where(o => o.OrderNumber == orderNumber && o.IsActive)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "根据订单号获取订单失败，订单号: {OrderNumber}", orderNumber);
            throw;
        }
    }

    public async Task<List<Order>> GetByStatusAsync(OrderStatus status)
    {
        try
        {
            return await _freeSql.Select<Order>()
                .Where(o => o.Status == status && o.IsActive)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "根据状态获取订单列表失败，状态: {Status}", status);
            throw;
        }
    }

    public async Task<(int TotalOrders, decimal TotalAmount)> GetUserOrderStatsAsync(int userId)
    {
        try
        {
            var stats = await _freeSql.Select<Order>()
                .Where(o => o.UserId == userId && o.Status == OrderStatus.Completed && o.IsActive)
                .GroupBy(o => o.UserId)
                .ToOneAsync(g => new
                {
                    TotalOrders = g.Count(),
                    TotalAmount = g.Sum(o => o.TotalAmount)
                });

            return (stats?.TotalOrders ?? 0, stats?.TotalAmount ?? 0);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "获取用户订单统计信息失败，用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<List<Order>> GetOrdersByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            return await _freeSql.Select<Order>()
                .Where(o => o.CreatedAt >= startDate && o.CreatedAt <= endDate && o.IsActive)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "获取指定时间范围内的订单列表失败，开始时间: {StartDate}, 结束时间: {EndDate}", startDate, endDate);
            throw;
        }
    }

    public async Task<bool> UpdateStatusAsync(int orderId, OrderStatus status)
    {
        try
        {
            var updateBuilder = _freeSql.Update<Order>()
                .Set(o => o.Status, status)
                .Set(o => o.UpdatedAt, DateTime.Now);

            // 根据状态设置相应的时间字段
            if (status == OrderStatus.Paid)
            {
                updateBuilder = updateBuilder.Set(o => o.PaidAt, DateTime.Now);
            }
            else if (status == OrderStatus.Completed)
            {
                updateBuilder = updateBuilder.Set(o => o.CompletedAt, DateTime.Now);
            }

            var affectedRows = await updateBuilder
                .Where(o => o.Id == orderId)
                .ExecuteAffrowsAsync();

            var success = affectedRows > 0;
            _logger.Information("更新订单状态{Result}，订单ID: {OrderId}, 新状态: {Status}", 
                success ? "成功" : "失败", orderId, status);
            return success;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "更新订单状态失败，订单ID: {OrderId}, 状态: {Status}", orderId, status);
            throw;
        }
    }

    public async Task<Order?> GetOrderWithItemsAsync(int orderId)
    {
        try
        {
            return await _freeSql.Select<Order>()
                .Where(o => o.Id == orderId && o.IsActive)
                .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
                .Include(o => o.User)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "获取订单详情失败，订单ID: {OrderId}", orderId);
            throw;
        }
    }
}
