using MemberSalesSystem.Common.Enums;
using MemberSalesSystem.Data.Entities;

namespace MemberSalesSystem.Data.Repositories;

/// <summary>
/// 充值记录仓储接口
/// </summary>
public interface IRechargeRepository : IRepository<Recharge>
{
    /// <summary>
    /// 根据用户ID获取充值记录列表
    /// </summary>
    Task<List<Recharge>> GetByUserIdAsync(int userId);
    
    /// <summary>
    /// 根据充值单号获取充值记录
    /// </summary>
    Task<Recharge?> GetByRechargeNumberAsync(string rechargeNumber);
    
    /// <summary>
    /// 根据状态获取充值记录列表
    /// </summary>
    Task<List<Recharge>> GetByStatusAsync(RechargeStatus status);
    
    /// <summary>
    /// 获取待审核的充值记录列表
    /// </summary>
    Task<List<Recharge>> GetPendingRechargesAsync();
    
    /// <summary>
    /// 更新充值记录状态
    /// </summary>
    Task<bool> UpdateStatusAsync(int rechargeId, RechargeStatus status, int reviewedById, string? reviewRemarks = null);
    
    /// <summary>
    /// 获取用户的充值统计信息
    /// </summary>
    Task<(int TotalRecharges, decimal TotalAmount)> GetUserRechargeStatsAsync(int userId);
    
    /// <summary>
    /// 获取指定时间范围内的充值记录列表
    /// </summary>
    Task<List<Recharge>> GetRechargesByDateRangeAsync(DateTime startDate, DateTime endDate);
}
