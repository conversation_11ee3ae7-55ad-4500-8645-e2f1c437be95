namespace MemberSalesSystem.Common.Models;

/// <summary>
/// 服务结果类
/// </summary>
public class ServiceResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? ErrorCode { get; set; }

    public static ServiceResult Ok(string message = "操作成功")
    {
        return new ServiceResult { Success = true, Message = message };
    }

    public static ServiceResult Fail(string message, string? errorCode = null)
    {
        return new ServiceResult { Success = false, Message = message, ErrorCode = errorCode };
    }
}

/// <summary>
/// 泛型服务结果类
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class ServiceResult<T> : ServiceResult
{
    public T? Data { get; set; }

    public static ServiceResult<T> Ok(T data, string message = "操作成功")
    {
        return new ServiceResult<T> { Success = true, Message = message, Data = data };
    }

    public static new ServiceResult<T> Fail(string message, string? errorCode = null)
    {
        return new ServiceResult<T> { Success = false, Message = message, ErrorCode = errorCode };
    }
}

/// <summary>
/// 分页结果类
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageIndex { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => PageIndex > 1;
    public bool HasNextPage => PageIndex < TotalPages;
}
