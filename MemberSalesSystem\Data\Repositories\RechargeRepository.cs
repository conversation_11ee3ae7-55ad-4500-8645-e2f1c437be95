using FreeSql;
using MemberSalesSystem.Common.Enums;
using MemberSalesSystem.Data.Entities;
using Serilog;

namespace MemberSalesSystem.Data.Repositories;

/// <summary>
/// 充值记录仓储实现
/// </summary>
public class RechargeRepository : Repository<Recharge>, IRechargeRepository
{
    public RechargeRepository(IFreeSql freeSql, ILogger logger) : base(freeSql, logger)
    {
    }

    public async Task<List<Recharge>> GetByUserIdAsync(int userId)
    {
        try
        {
            return await _freeSql.Select<Recharge>()
                .Where(r => r.UserId == userId && r.IsActive)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "根据用户ID获取充值记录列表失败，用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<Recharge?> GetByRechargeNumberAsync(string rechargeNumber)
    {
        try
        {
            return await _freeSql.Select<Recharge>()
                .Where(r => r.RechargeNumber == rechargeNumber && r.IsActive)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "根据充值单号获取充值记录失败，充值单号: {RechargeNumber}", rechargeNumber);
            throw;
        }
    }

    public async Task<List<Recharge>> GetByStatusAsync(RechargeStatus status)
    {
        try
        {
            return await _freeSql.Select<Recharge>()
                .Where(r => r.Status == status && r.IsActive)
                .Include(r => r.User)
                .Include(r => r.ReviewedBy)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "根据状态获取充值记录列表失败，状态: {Status}", status);
            throw;
        }
    }

    public async Task<List<Recharge>> GetPendingRechargesAsync()
    {
        try
        {
            return await _freeSql.Select<Recharge>()
                .Where(r => r.Status == RechargeStatus.Pending && r.IsActive)
                .Include(r => r.User)
                .OrderBy(r => r.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "获取待审核充值记录列表失败");
            throw;
        }
    }

    public async Task<bool> UpdateStatusAsync(int rechargeId, RechargeStatus status, int reviewedById, string? reviewRemarks = null)
    {
        try
        {
            var affectedRows = await _freeSql.Update<Recharge>()
                .Set(r => r.Status, status)
                .Set(r => r.ReviewedById, reviewedById)
                .Set(r => r.ReviewedAt, DateTime.Now)
                .Set(r => r.ReviewRemarks, reviewRemarks)
                .Set(r => r.UpdatedAt, DateTime.Now)
                .Where(r => r.Id == rechargeId)
                .ExecuteAffrowsAsync();

            var success = affectedRows > 0;
            _logger.Information("更新充值记录状态{Result}，充值ID: {RechargeId}, 新状态: {Status}, 审核人: {ReviewedById}", 
                success ? "成功" : "失败", rechargeId, status, reviewedById);
            return success;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "更新充值记录状态失败，充值ID: {RechargeId}, 状态: {Status}", rechargeId, status);
            throw;
        }
    }

    public async Task<(int TotalRecharges, decimal TotalAmount)> GetUserRechargeStatsAsync(int userId)
    {
        try
        {
            var stats = await _freeSql.Select<Recharge>()
                .Where(r => r.UserId == userId && r.Status == RechargeStatus.Approved && r.IsActive)
                .GroupBy(r => r.UserId)
                .ToOneAsync(g => new
                {
                    TotalRecharges = g.Count(),
                    TotalAmount = g.Sum(r => r.Amount)
                });

            return (stats?.TotalRecharges ?? 0, stats?.TotalAmount ?? 0);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "获取用户充值统计信息失败，用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<List<Recharge>> GetRechargesByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            return await _freeSql.Select<Recharge>()
                .Where(r => r.CreatedAt >= startDate && r.CreatedAt <= endDate && r.IsActive)
                .Include(r => r.User)
                .Include(r => r.ReviewedBy)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "获取指定时间范围内的充值记录列表失败，开始时间: {StartDate}, 结束时间: {EndDate}", startDate, endDate);
            throw;
        }
    }
}
